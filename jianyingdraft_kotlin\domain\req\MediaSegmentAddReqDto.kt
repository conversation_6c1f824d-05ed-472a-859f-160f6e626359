package com.esther.jianyingdraft.domain.req

import com.esther.jianyingdraft.domain.ClipSettings
import com.esther.jianyingdraft.domain.Timerange
import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 音频/视频的片段
 */
@Schema(description = "音频/视频片段添加请求参数")
data class MediaSegmentAddReqDto(
    @Schema(description = "素材片段添加到哪个片段之后, 可以是任意类型素材的id")
    val afterSegmentId: String? = null,

    @Schema(description = "素材所属的 draftId")
    val draftId: String,
    @Schema(description = "片段在轨道上的目标时间范围")
    val targetTimerange: Timerange = Timerange(),
    @Schema(description = "截取的素材片段的时间范围, 默认从开头根据speed截取与target_timerange等长的一部分")
    val sourceTimerange: Timerange? = null,
    @Schema(description = "播放速度, 默认为1.0. 此项与source_timerange同时指定时, 将覆盖target_timerange中的时长")
    val speed: Double = 1.0,
    @Schema(description = "音量, 默认为1.0")
    val volume: Double = 1.0,
    @Schema(description = "素材实例或素材路径, 这里对应的是一个网络路径, 最后还需要下载的")
    val resourcePath: String,

    /**
     * 轨道id, 把视频添加到哪个轨道
     */
    @Schema(description = "轨道id")
    val trackId: String? = null,

    @Schema(description = "素材片段的配置项, 默认为空")
    /**
     * 素材片段的配置项, 默认为空
     */
    val clipSettings: ClipSettings? = null,

    )
