package com.esther.jianyingdraft.utils

import com.esther.jianyingdraft.domain.meterial.MediaInfo
import com.esther.jianyingdraft.utils.TimeUtils.SEC
import org.apache.tika.Tika
import org.apache.tika.metadata.Metadata
import org.apache.tika.metadata.XMPDM
import org.apache.tika.parser.AutoDetectParser
import org.apache.tika.sax.BodyContentHandler
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.net.URI
import java.net.URL
import java.net.URLConnection

/**
 * 媒体文件信息提取工具类
 * 使用Apache Tika获取媒体文件的完整信息：文件名、路径、时长(微秒)、大小、格式、类型、宽高
 *
 * 特别针对GIF动图：根据帧数和延迟时间正确计算时长
 * GIF时长 = 帧数 × 延迟时间(1/100秒) × 10毫秒
 *
 * 媒体类型分类：
 * - video: 视频文件和GIF动图
 * - photo: 静态图片
 * - audio: 音频文件
 * 支持本地文件路径和网络URL
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
object MediaInfoExtractor {

    private val logger = LoggerFactory.getLogger(MediaInfoExtractor::class.java)
    private val parser = AutoDetectParser()
    private val tika = Tika()

    /**
     * 获取媒体文件的完整信息：文件名、绝对路径、类型、宽高、时长(微秒)、大小、格式
     * 自动识别本地路径或网络URL
     *
     * 支持的格式：
     * - 视频：MP4, AVI, MOV, WMV等 (type=video)
     * - 音频：MP3, WAV, AAC, FLAC等 (type=audio)
     * - 动图：GIF(特殊计算), WebP等 (type=video)
     * - 图片：JPEG, PNG等 (type=photo, 无时长信息)
     *
     * @param filePath 文件路径（本地路径或网络URL）
     * @return MediaInfo对象，包含完整媒体信息，如果解析失败返回错误信息
     */
    fun extractMediaInfo(filePath: String): MediaInfoResult {
        return try {
            logger.info("开始解析媒体文件: {}", filePath)

            if (NetUtils.isNetworkPath(filePath)) {
                extractFromUrl(filePath)
            } else {
                extractFromLocalFile(filePath)
            }

        } catch (e: IOException) {
            logger.error("解析媒体文件时发生IO异常: {}", filePath, e)
            MediaInfoResult.error("Failed to parse file: ${e.message}")
        } catch (e: Exception) {
            logger.error("解析媒体文件时发生未知异常: {}", filePath, e)
            MediaInfoResult.error("Failed to parse file: ${e.message}")
        }
    }

    /**
     * 从本地文件提取媒体信息
     *
     * @param filePath 本地文件路径
     * @return 媒体信息结果
     */
    private fun extractFromLocalFile(filePath: String): MediaInfoResult {
        logger.debug("处理本地文件: {}", filePath)

        val file = File(filePath)
        if (!file.exists()) {
            logger.error("本地文件不存在: {}", filePath)
            return MediaInfoResult.error("File not found: $filePath")
        }

        val metadata = Metadata()
        val handler = BodyContentHandler(-1)

        FileInputStream(file).use { inputStream ->
            parser.parse(inputStream, handler, metadata)
        }

        val mediaInfo = buildMediaInfoFromFile(file, metadata)
        logger.info("本地媒体文件解析完成: {}", filePath)

        return MediaInfoResult.success(mediaInfo)
    }

    /**
     * 从网络URL提取媒体信息
     *
     * @param urlPath 网络URL路径
     * @return 媒体信息结果
     */
    private fun extractFromUrl(urlPath: String): MediaInfoResult {
        logger.debug("处理网络URL: {}", urlPath)

        val url = URI(urlPath).toURL()
        val connection = createUrlConnection(url)

        val metadata = Metadata()
        val handler = BodyContentHandler(-1)

        connection.inputStream.use { inputStream ->
            parser.parse(inputStream, handler, metadata)
        }

        val mediaInfo = buildMediaInfoFromUrl(url, connection, metadata)
        logger.info("网络媒体文件解析完成: {}", urlPath)

        return MediaInfoResult.success(mediaInfo)
    }

    /**
     * 创建URL连接并设置必要的属性
     *
     * @param url URL对象
     * @return 配置好的URLConnection
     */
    private fun createUrlConnection(url: URL): URLConnection {
        val connection = url.openConnection()

        // 设置连接超时（10秒）
        connection.connectTimeout = 10000
        // 设置读取超时（30秒）
        connection.readTimeout = 30000
        // 设置User-Agent避免被某些服务器拒绝
        connection.setRequestProperty(
            "User-Agent",
            "MediaInfoExtractor/1.0 (Apache Tika)"
        )

        logger.debug(
            "URL连接配置完成，Content-Type: {}, Content-Length: {}",
            connection.contentType, connection.contentLength
        )

        return connection
    }

    /**
     * 构建本地文件媒体信息
     *
     * @param file 文件对象
     * @param metadata Tika解析的元数据
     * @return MediaInfo对象
     */
    private fun buildMediaInfoFromFile(file: File, metadata: Metadata): MediaInfo {
        val fileName = file.name
        val absolutePath = file.absolutePath
        //val fileSize = formatFileSize(file.length())
        val fileSize = file.length()
        val mimeType = tika.detect(file) ?: "unknown"

        // 判断媒体类型
        val type = determineMediaType(mimeType)

        // 提取宽高信息
        val (width, height) = extractDimensionInfo(metadata)

        // 提取时长信息
        val durationMicroseconds = extractDurationInfo(metadata, mimeType)
        val durationSeconds = durationMicroseconds?.div(1_000_000.0)?.toString() + "s"
        return MediaInfo(
            fileName = fileName,
            absolutePath = absolutePath,
            fileSize = fileSize,
            mimeType = mimeType,
            type = type,
            width = width,
            height = height,
            durationMicroseconds = durationMicroseconds,
            durationSeconds = durationSeconds
        )
    }

    /**
     * 构建网络URL媒体信息
     *
     * @param url URL对象
     * @param connection URL连接对象
     * @param metadata Tika解析的元数据
     * @return MediaInfo对象
     */
    private fun buildMediaInfoFromUrl(url: URL, connection: URLConnection, metadata: Metadata): MediaInfo {
        val absolutePath = url.toString()
        val fileName = url.path.substringAfterLast('/').ifEmpty { "unknown" }
        val fileSize = if (connection.contentLength > 0) {
            connection.contentLength.toLong()
        } else {
            0L
        }
        val mimeType = connection.contentType ?: tika.detect(url) ?: "unknown"

        // 判断媒体类型
        val type = determineMediaType(mimeType)

        // 提取宽高信息
        val (width, height) = extractDimensionInfo(metadata)

        // 提取时长信息
        val durationMicroseconds = extractDurationInfo(metadata, mimeType)
        val durationSeconds = durationMicroseconds?.div(TimeUtils.SEC.toDouble())

        return MediaInfo(
            fileName = fileName,
            absolutePath = absolutePath,
            fileSize = fileSize,
            mimeType = mimeType,
            type = type,
            width = width,
            height = height,
            durationMicroseconds = durationMicroseconds,
            durationSeconds = if (durationSeconds != null) "${durationSeconds}s" else null
        )
    }

    /**
     * 提取时长信息 - 支持多种媒体格式
     *
     * @param metadata Tika元数据对象
     * @param mimeType MIME类型
     * @return 时长(微秒)，如果无时长则返回null
     */
    private fun extractDurationInfo(metadata: Metadata, mimeType: String): Long? {
        val mimeTypeLower = mimeType.lowercase()

        // 1. GIF动图特殊处理
        if (mimeTypeLower.contains("gif")) {
            return extractGifDuration(metadata)
        }

        // 2. 标准视频/音频时长
        metadata.get(XMPDM.DURATION)?.let { duration ->
            return formatDuration(duration)
        }

        // 3. 其他常见时长字段
        val durationFields = listOf(
            "duration", "Duration", "DURATION",
            "xmpDM:duration", "tiff:ImageLength"
        )

        for (field in durationFields) {
            metadata.get(field)?.let { duration ->
                return formatDuration(duration)
            }
        }

        // 4. 对于静态图片，默认2s
        if (mimeTypeLower.startsWith("image/")) {
            return 2 * SEC
        }

        // 5. 其他格式未找到时长信息
        return null
    }

    /**
     * 判断媒体类型
     *
     * @param mimeType MIME类型
     * @return 媒体类型 (video/photo/audio)
     */
    private fun determineMediaType(mimeType: String): String {
        val mimeTypeLower = mimeType.lowercase()

        return when {
            // GIF动图和视频都归类为video
            mimeTypeLower.contains("gif") -> "video"
            mimeTypeLower.startsWith("video/") -> "video"
            // 音频
            mimeTypeLower.startsWith("audio/") -> "audio"
            // 其他图片类型归类为photo
            mimeTypeLower.startsWith("image/") -> "photo"
            // 未知类型根据扩展名猜测
            else -> {
                when {
                    mimeTypeLower.contains("mp4") || mimeTypeLower.contains("avi") ||
                            mimeTypeLower.contains("mov") || mimeTypeLower.contains("wmv") -> "video"

                    mimeTypeLower.contains("mp3") || mimeTypeLower.contains("wav") ||
                            mimeTypeLower.contains("aac") -> "audio"

                    mimeTypeLower.contains("jpg") || mimeTypeLower.contains("png") ||
                            mimeTypeLower.contains("jpeg") -> "photo"

                    else -> "photo" // 默认为photo
                }
            }
        }
    }

    /**
     * 提取宽高信息
     *
     * @param metadata Tika元数据对象
     * @return Pair<宽度, 高度>，如果无法获取则返回null
     */
    private fun extractDimensionInfo(metadata: Metadata): Pair<Int?, Int?> {
        try {
            // 调试：打印所有元数据键用于分析
            logger.debug(
                "尺寸相关元数据键: {}",
                metadata.names().filter { name ->
                    val nameLower = name.lowercase()
                    nameLower.contains("width") || nameLower.contains("height") ||
                            nameLower.contains("dimension") || nameLower.contains("image")
                }.joinToString(", ")
            )

            // 尝试多种可能的宽度字段名
            val widthFields = listOf(
                "width", "Width", "WIDTH",
                "tiff:ImageWidth", "Image Width", "ImageWidth",
                "Dimension ImageWidth", "Dimension HorizontalPixelOffset",
                "imagereader:Width", "Video Width", "Frame Width"
            )

            var width: Int? = null
            for (field in widthFields) {
                val fieldValue = metadata.get(field)?.toIntOrNull()
                if (fieldValue != null) {
                    width = fieldValue
                    logger.debug("从字段 '{}' 获取宽度: {}", field, width)
                    break
                }
            }

            // 尝试多种可能的高度字段名
            val heightFields = listOf(
                "height", "Height", "HEIGHT",
                "tiff:ImageLength", "Image Height", "ImageHeight", "ImageLength",
                "Dimension ImageHeight", "Dimension VerticalPixelOffset",
                "imagereader:Height", "Video Height", "Frame Height"
            )

            var height: Int? = null
            for (field in heightFields) {
                val fieldValue = metadata.get(field)?.toIntOrNull()
                if (fieldValue != null) {
                    height = fieldValue
                    logger.debug("从字段 '{}' 获取高度: {}", field, height)
                    break
                }
            }

            logger.info("提取到尺寸信息 - 宽度: {}, 高度: {}", width, height)

            return Pair(width, height)

        } catch (e: Exception) {
            logger.warn("解析尺寸信息时发生异常", e)
            return Pair(null, null)
        }
    }

    /**
     * 提取GIF动图时长
     * 根据帧数和延迟时间计算总时长
     *
     * @param metadata Tika元数据对象
     * @return GIF时长(微秒)，如果无法计算则返回null
     */
    private fun extractGifDuration(metadata: Metadata): Long? {
        try {
            // 调试：打印所有元数据键
            logger.debug("所有元数据键: {}", metadata.names().joinToString(", "))

            // 获取图片数量 - 尝试多种可能的字段名
            val numImages = metadata.get("imagereader:NumImages")?.toIntOrNull()
                ?: metadata.get("tiff:ImageLength")?.toIntOrNull()
                ?: metadata.get("NumImages")?.toIntOrNull()
                ?: metadata.get("Image Count")?.toIntOrNull()

            // 获取延迟时间 (1/100秒为单位) - 尝试多种可能的字段名
            var delayTime: Int? = null

            // 遍历所有元数据寻找delayTime
            for (name in metadata.names()) {
                val value = metadata.get(name)
                if (name.contains("delayTime", ignoreCase = true) ||
                    value?.contains("delayTime=") == true
                ) {

                    // 如果值包含delayTime=，提取数字
                    if (value.contains("delayTime=")) {
                        val delayMatch = "delayTime=(\\d+)".toRegex().find(value)
                        delayTime = delayMatch?.groupValues?.get(1)?.toIntOrNull()
                        logger.debug("从 '{}' 中提取到延迟时间: {}", value, delayTime)
                        break
                    } else {
                        delayTime = value.toIntOrNull()
                        if (delayTime != null) {
                            logger.debug("从字段 '{}' 中获取延迟时间: {}", name, delayTime)
                            break
                        }
                    }
                }
            }

            logger.info("GIF信息 - 图片数量: {}, 延迟时间: {} (1/100s)", numImages, delayTime)

            if (numImages != null && delayTime != null && numImages > 0 && delayTime > 0) {
                // 计算总时长：图片数量 * 延迟时间 * 10毫秒 * 1000微秒
                val totalMilliseconds = numImages * delayTime * 10L
                val totalMicroseconds = totalMilliseconds * 1000L

                logger.info(
                    "GIF计算时长: {} 帧 × {} (1/100s) × 10ms = {} 毫秒 = {} 微秒",
                    numImages, delayTime, totalMilliseconds, totalMicroseconds
                )
                return totalMicroseconds
            }

            // 如果只有图片数量，假设每帧100ms (delayTime=10)
            if (numImages != null && numImages > 1) {
                val defaultDelayMs = 100L // 默认每帧100毫秒
                val totalMicroseconds = numImages * defaultDelayMs * 1000L
                logger.info(
                    "GIF使用默认延迟计算时长: {} 帧 × {}ms = {} 微秒",
                    numImages,
                    defaultDelayMs,
                    totalMicroseconds
                )
                return totalMicroseconds
            }

            logger.warn("GIF时长计算失败 - 图片数量: {}, 延迟时间: {}", numImages, delayTime)

        } catch (e: Exception) {
            logger.warn("解析GIF时长时发生异常", e)
        }

        return null
    }


    /**
     * 格式化文件大小
     *
     * @param bytes 字节数
     * @return 格式化后的文件大小字符串
     */
    private fun formatFileSize(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0

        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }

        return String.format("%.2f %s", size, units[unitIndex])
    }

    /**
     * 格式化时长信息为微秒
     *
     * @param duration 时长字符串（秒）
     * @return 时长微秒数
     */
    private fun formatDuration(duration: String): Long {
        return try {
            val seconds = duration.toDouble()
            // 转换为微秒 (1秒 = 1,000,000微秒)
            (seconds * 1_000_000).toLong()
        } catch (e: NumberFormatException) {
            0L // 解析失败返回0微秒
        }
    }
}


/**
 * 媒体信息提取结果封装类
 */
data class MediaInfoResult(
    val success: Boolean,
    val data: MediaInfo?,
    val errorMessage: String?
) {
    companion object {
        fun success(data: MediaInfo) = MediaInfoResult(true, data, null)
        fun error(message: String) = MediaInfoResult(false, null, message)
    }
} 