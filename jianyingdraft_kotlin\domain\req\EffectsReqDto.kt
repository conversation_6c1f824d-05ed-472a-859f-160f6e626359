package com.esther.jianyingdraft.domain.req

import com.esther.jianyingdraft.domain.ClipSettings
import com.esther.jianyingdraft.domain.Timerange
import com.esther.jianyingdraft.domain.effect.AudioFadeEffect
import com.esther.jianyingdraft.domain.text.TextBackground
import com.esther.jianyingdraft.domain.text.TextBorder
import com.esther.jianyingdraft.domain.text.TextStyle
import com.esther.jianyingdraft.utils.TimeUtils
import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */

/**
 * 资源信息, id和名字
 */
@Schema(description = "资源信息, id和名字")
data class Resource(
    /**
     * 资源id
     */
    @Schema(description = "资源id")
    val resourceId: String,

    /**
     * 资源名字
     */
    @Schema(description = "资源名字")
    val resourceName: String,
)

//============================音频特效============================
/**
 * 音频淡入淡出特效
 */
@Schema(description = "音频淡入淡出特效请求参数")
data class AudioFadeEffectReqDto(
    /**
     *  草稿id
     */
    @Schema(description = "草稿id")
    val draftId: String,
    /**
     * 音频片段id
     */
    @Schema(description = "音频片段id")
    val audioSegmentId: String,
    /**
     * 音频淡入淡出效果
     */
    @Schema(description = "音频淡入淡出效果")
    val audioFade: AudioFadeEffect,
)
// todo 差一个音频的特效

@Schema(description = "音频关键帧请求参数")
data class AudioKeyframeReqDto(
    /**
     *  草稿id
     */
    @Schema(description = "草稿id")
    val draftId: String,
    /**
     * 音频片段id
     */
    @Schema(description = "音频片段id")
    val audioSegmentId: String,
    /**
     * 关键帧列表，支持批量添加多个关键帧
     */
    @Schema(description = "关键帧列表，支持批量添加多个关键帧")
    val keyframes: List<KeyframeData>
)

/**
 * 单个关键帧数据
 */
@Schema(description = "单个关键帧数据")
data class KeyframeData(
    /**
     * 关键帧的时间偏移量, 1s,2s
     */
    @Schema(description = "关键帧的时间偏移量, 1s,2s")
    val timeOffset: String,
    /**
     * 音量在`time_offset`处的值
     */
    @Schema(description = "音量在`time_offset`处的值")
    val volume: Float = 1.0f,
)

//============================视频特效============================

data class VideoAnimation(
    /**
     * 动画类型
     */
    val type: Resource,

    /**
     * 动画的时间
     */
    val duration: String? = null,

    /**
     * 动画的时间范围
     */
    val realDuration: Long? = duration?.let { TimeUtils.tim(it) }
)

@Schema(description = "视频特效请求参数")
data class VideoAnimationReqDto(
    /**
     * 动画类型
     */
    @Schema(description = "动画类型")
    val type: Resource,
    /**
     * 动画的时间范围
     */
    @Schema(description = "动画的时间范围")
    val duration: String? = null,
    /**
     *  草稿id
     */
    @Schema(description = "草稿id")
    val draftId: String,
    /**
     * 视频片段id
     */
    @Schema(description = "视频片段id")
    val videoSegmentId: String,
)

data class BackgroundFilling(
    /**
     * 背景填充类型
     */
    val fillType: String = "blur", // blur,color
    /**
     * 模糊度, 0~1
     */
    val blur: Double = 0.625,
    /**
     * 填充颜色
     */
    val color: String = "#00000000"
)

/**
 * 背景填充特效
 */
@Schema(description = "背景填充请求参数")
data class BackgroundFillingReqDto(
    /**
     *  草稿id
     */
    @Schema(description = "草稿id")
    val draftId: String,
    /**
     * 视频片段id
     */
    @Schema(description = "视频片段id")
    val videoSegmentId: String,
    /**
     * 背景填充类型
     */
    @Schema(description = "背景填充类型")
    val fillType: String = "blur", // blur,color
    /**
     * 模糊度, 0~1
     */
    @Schema(description = "模糊度, 0~1")
    val blur: Double = 0.625,
    /**
     * 填充颜色
     */
    @Schema(description = "填充颜色")
    val color: String = "#00000000"
)

data class TransitionType(
    /**
     * 转场类型
     */
    val transitionType: Resource,
    /**
     * 转场持续时间, 单位为微秒. 若传入字符串则会调用`tim()`函数进行解析. 若不指定则使用转场类型定义的默认值.
     */
    val duration: String? = null,

    /**
     * 动画的时间范围
     */
    val realDuration: Long? = duration?.let { TimeUtils.tim(it) }
)

/**
 * 转场特效
 */
@Schema(description = "转场特效请求参数")
data class TransitionTypeReqDto(
    /**
     *  草稿id
     */
    @Schema(description = "草稿id")
    val draftId: String,
    /**
     * 视频片段id
     */
    @Schema(description = "视频片段id")
    val videoSegmentId: String,
    /**
     * 转场类型
     */
    @Schema(description = "转场类型")
    val transitionType: Resource,
    /**
     * 转场持续时间, 单位为微秒. 若传入字符串则会调用`tim()`函数进行解析. 若不指定则使用转场类型定义的默认值.
     */
    @Schema(description = "转场持续时间, 单位为微秒. 若传入字符串则会调用`tim()`函数进行解析. 若不指定则使用转场类型定义的默认值.")
    val duration: String? = null,

    /**
     * 读取的转场持续时间
     */
    @Schema(description = "读取的转场持续时间")
    val realDuration: Long = duration?.let { TimeUtils.tim(it) } ?: 0
)

// ============================字幕特效============================

@Schema(description = "字幕特效请求参数")
data class TextSegmentAddReqDto(

    @Schema(description = "素材片段添加到哪个片段之后, 可以是任意类型素材的id")
    val afterSegmentId: String? = null,

    /**
     * 字幕片段id
     */
    @Schema(description = "字幕片段id")
    val textSegmentId: String? = null,
    /**
     *  草稿id
     */
    @Schema(description = "草稿id")
    val draftId: String,
    /**
     * 文本
     */
    @Schema(description = "文本")
    var text: String,
    /**
     * 字体
     */
    @Schema(description = "字体")
    var font: Resource? = null,
    /**
     * 样式
     */
    @Schema(description = "样式")
    var style: TextStyle? = null,
    /**
     * 边框
     */
    @Schema(description = "边框")
    var border: TextBorder? = null,
    /**
     * 裁剪设置
     */
    @Schema(description = "裁剪设置")
    var clipSettings: ClipSettings? = null,
    /**
     * 背景
     */
    @Schema(description = "背景")
    var background: TextBackground? = null,

    /**
     * 轨道id, 把资源添加到哪个轨道
     */
    @Schema(description = "轨道id")
    val trackId: String? = null,

    @Schema(description = "目标时间范围")
    val targetRanger: Timerange? = null,
)

/**
 * 字幕动画和资源特效
 */
data class TextAnimationAndEffect(
    /**
     * 动画类型  00
     */
    val type: Resource? = null,

    /**
     * 动画持续时间, 单位为微秒, 仅对入场/出场动画有效.  00
     */
    val duration: String? = null,


    /**
     * 泡泡特效id  11
     */
    val bubbleEffectId: String? = null,
    /**
     * 泡泡特效资源id  11
     */
    val bubbleResourceId: String? = null,


    /**
     * 花字特效资源id 22
     */
    val flowerEffectId: String? = null,
)

/**
 * 字幕动画和特效请求参数
 */
@Schema(description = "字幕动画和特效请求参数")
data class TextAnimationAndEffectReqDto(
    /**
     * 字幕片段id
     */
    val textSegmentId: String,
    /**
     *  草稿id
     */
    val draftId: String,
    /**
     * 动画类型 00
     */
    @Schema(description = "动画类型 00")
    val type: Resource? = null,

    /**
     * 动画持续时间, 单位为微秒, 仅对入场/出场动画有效.  00
     */
    @Schema(description = "动画持续时间, 单位为秒, 仅对入场/出场动画有效.  00")
    val duration: String? = null,


    /**
     * 泡泡特效id  11
     */
    @Schema(description = "泡泡特效id  11")
    val bubbleEffectId: String? = null,
    /**
     * 泡泡特效资源id  11
     */
    @Schema(description = "泡泡特效资源id  11")
    val bubbleResourceId: String? = null,


    /**
     * 花字特效资源id 22
     */
    @Schema(description = "花字特效资源id 22")
    val flowerEffectId: String? = null,
)
