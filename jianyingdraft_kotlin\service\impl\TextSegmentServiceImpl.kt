package com.esther.jianyingdraft.service.impl

import com.esther.jianyingdraft.domain.req.TextSegmentAddReqDto
import com.esther.jianyingdraft.domain.text.TextBackground
import com.esther.jianyingdraft.domain.text.TextBorder
import com.esther.jianyingdraft.domain.text.TextStyle
import com.esther.jianyingdraft.domain.req.TextAnimationAndEffectReqDto
import com.esther.jianyingdraft.domain.req.TextAnimationAndEffect
import com.esther.jianyingdraft.entity.TextSegmentEntity
import com.esther.jianyingdraft.service.TextSegmentService
import com.esther.jianyingdraft.utils.DraftUtils
import com.esther.jianyingdraft.utils.TimeUtils
import kotlinx.coroutines.reactive.awaitSingle
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import kotlinx.coroutines.reactor.awaitSingleOrNull

/**
 * 字幕片段服务实现类，实现字幕片段相关操作。
 * <AUTHOR>
 */
@Service
class TextSegmentServiceImpl @Autowired constructor(
    private val reactiveMongoTemplate: ReactiveMongoTemplate
) : TextSegmentService {
    private val logger = LoggerFactory.getLogger(TextSegmentServiceImpl::class.java)

    /**
     * 添加字幕片段到MongoDB
     * @param req 字幕片段添加请求参数
     * @return 新增字幕片段的id
     */
    override suspend fun addTextSegment(req: TextSegmentAddReqDto): String {
        logger.info(
            "开始添加/更新字幕片段，draftId={}, textSegmentId={}, text={}, afterSegmentId={}",
            req.draftId,
            req.textSegmentId,
            req.text,
            req.afterSegmentId
        )
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        val now = LocalDateTime.now()
        val id = req.textSegmentId ?: ObjectId().toHexString()
        if (req.textSegmentId != null) {
            // 更新逻辑
            val query = Query()
                .addCriteria(Criteria.where("id").`is`(req.textSegmentId))
                .addCriteria(Criteria.where("draftId").`is`(req.draftId))
            val old = reactiveMongoTemplate.findOne(query, TextSegmentEntity::class.java).awaitSingleOrNull()
            if (old != null) {
                val mergedStyle = mergeTextStyle(old.style, req.style)
                val mergedBorder = mergeTextBorder(old.border, req.border)
                val mergedBackground = mergeTextBackground(old.background, req.background)
                
                // 处理时间范围更新（如果提供了新的时间范围）
                val updatedTargetRanger = if (req.targetRanger != null) {
                    // 如果提供了新的时间范围，需要重新计算完整的时间范围
                    val completeTargetTimerange = DraftUtils.buildCompleteTargetTimerange(
                        originalTimerange = req.targetRanger,
                        afterSegmentId = req.afterSegmentId,
                        calculatedDuration = req.targetRanger.duration ?: "5s", // 默认5秒
                        mongoTemplate = reactiveMongoTemplate
                    )
                    logger.debug("更新文本片段时间范围: start={}, duration={}", 
                        completeTargetTimerange.start, completeTargetTimerange.duration)
                    
                    // 检查更新后的时间范围是否与其他片段重叠
                    DraftUtils.checkSegmentOverlapInTrack(
                        draftId = req.draftId,
                        trackId = req.trackId ?: old.trackId,
                        newSegmentTimerange = completeTargetTimerange,
                        segmentType = "text",
                        excludeSegmentId = req.textSegmentId, // 更新操作，排除当前片段
                        mongoTemplate = reactiveMongoTemplate
                    )
                    
                    completeTargetTimerange
                } else {
                    old.targetRanger
                }
                
                val update = Update()
                    .set("text", req.text)
                    .set("font", req.font ?: old.font)
                    .set("style", mergedStyle)
                    .set("border", mergedBorder)
                    .set("clipSettings", req.clipSettings)
                    .set("background", mergedBackground)
                    .set("targetRanger", updatedTargetRanger)
                    .set("realTargetRanger", updatedTargetRanger?.let { TimeUtils.calculateTimeline(it) })
                    .set("updateTime", now)
                reactiveMongoTemplate.updateFirst(query, update, TextSegmentEntity::class.java).awaitSingle()
                logger.info("字幕片段更新成功，id={}", old.id)
                return old.id
            }
            // 如果找不到原片段，则走新增
        }
        
        // 新增逻辑 - 支持afterSegmentId自动时间计算
        val completeTargetRanger = if (req.targetRanger != null) {
            // 构建完整的目标时间范围（支持afterSegmentId自动计算start时间）
            val targetDuration = req.targetRanger.duration ?: "5s" // 文本片段默认5秒
            DraftUtils.buildCompleteTargetTimerange(
                originalTimerange = req.targetRanger,
                afterSegmentId = req.afterSegmentId,
                calculatedDuration = targetDuration,
                mongoTemplate = reactiveMongoTemplate
            )
        } else if (req.afterSegmentId != null) {
            // 如果只提供了afterSegmentId而没有targetRanger，则创建一个默认的时间范围
            DraftUtils.buildCompleteTargetTimerange(
                originalTimerange = com.esther.jianyingdraft.domain.Timerange(start = null, duration = "5s"),
                afterSegmentId = req.afterSegmentId,
                calculatedDuration = "5s",
                mongoTemplate = reactiveMongoTemplate
            )
        } else {
            req.targetRanger
        }
        
        logger.debug("文本片段完整时间范围: start={}, duration={}", 
            completeTargetRanger?.start, completeTargetRanger?.duration)
        
        // 检查同一轨道上是否存在重叠片段（仅当有时间范围时检查）
        if (completeTargetRanger != null) {
            DraftUtils.checkSegmentOverlapInTrack(
                draftId = req.draftId,
                trackId = req.trackId,
                newSegmentTimerange = completeTargetRanger,
                segmentType = "text",
                excludeSegmentId = null, // 新增操作，不排除任何片段
                mongoTemplate = reactiveMongoTemplate
            )
        }
        
        val entity = TextSegmentEntity(
            id = id,
            text = req.text,
            draftId = req.draftId,
            font = req.font,
            style = req.style,
            border = req.border,
            background = req.background,
            createTime = now,
            trackId = req.trackId,
            targetRanger = completeTargetRanger,
            realTargetRanger = completeTargetRanger?.let { TimeUtils.calculateTimeline(it) },
            updateTime = now
        ).apply {
            val clip = req.clipSettings
            if (clip != null) {
                clipSettings = clip
            }
        }
        reactiveMongoTemplate.save(entity).awaitSingle()
        logger.info("字幕片段添加成功，id={}, 最终时间范围: start={}, duration={}", 
            id, completeTargetRanger?.start, completeTargetRanger?.duration)
        return id
    }

    /**
     * 合并TextStyle对象，属性不同则用新值，否则保留旧值
     */
    private fun mergeTextStyle(old: TextStyle?, new: TextStyle?): TextStyle? {
        if (new == null) return old
        if (old == null) return new
        return TextStyle(
            size = if (new.size != old.size) new.size else old.size,
            bold = if (new.bold != old.bold) new.bold else old.bold,
            italic = if (new.italic != old.italic) new.italic else old.italic,
            underline = if (new.underline != old.underline) new.underline else old.underline,
            color = if (new.color != old.color) new.color else old.color,
            alpha = if (new.alpha != old.alpha) new.alpha else old.alpha,
            align = if (new.align != old.align) new.align else old.align,
            vertical = if (new.vertical != old.vertical) new.vertical else old.vertical,
            letterSpacing = if (new.letterSpacing != old.letterSpacing) new.letterSpacing else old.letterSpacing,
            lineSpacing = if (new.lineSpacing != old.lineSpacing) new.lineSpacing else old.lineSpacing,
            autoWrapping = if (new.autoWrapping != old.autoWrapping) new.autoWrapping else old.autoWrapping,
            maxLineWidth = if (new.maxLineWidth != old.maxLineWidth) new.maxLineWidth else old.maxLineWidth
        )
    }

    /**
     * 合并TextBorder对象，属性不同则用新值，否则保留旧值
     */
    private fun mergeTextBorder(old: TextBorder?, new: TextBorder?): TextBorder? {
        if (new == null) return old
        if (old == null) return new
        return TextBorder(
            alpha = if (new.alpha != old.alpha) new.alpha else old.alpha,
            color = if (new.color != old.color) new.color else old.color
            // width 只读，不能直接赋值
        )
    }

    /**
     * 合并TextBackground对象，属性不同则用新值，否则保留旧值
     */
    private fun mergeTextBackground(old: TextBackground?, new: TextBackground?): TextBackground? {
        if (new == null) return old
        if (old == null) return new
        return TextBackground(
            style = if (new.style != old.style) new.style else old.style,
            alpha = if (new.alpha != old.alpha) new.alpha else old.alpha,
            color = if (new.color != old.color) new.color else old.color,
            roundRadius = if (new.roundRadius != old.roundRadius) new.roundRadius else old.roundRadius,
            height = if (new.height != old.height) new.height else old.height,
            width = if (new.width != old.width) new.width else old.width
            // horizontal/vertical为private，offset为只读
        )
    }

    /**
     * 给字幕片段添加动画和特效到列表中
     * @param req 动画和特效请求参数
     * @return 字幕片段id
     */
    override suspend fun addOrUpdateTextAnimationAndEffectToSegment(req: TextAnimationAndEffectReqDto): String {
        logger.info("开始为字幕片段添加动画和特效，textSegmentId={}, draftId={}", req.textSegmentId, req.draftId)
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        val query = Query()
            .addCriteria(Criteria.where("id").`is`(req.textSegmentId))
            .addCriteria(Criteria.where("draftId").`is`(req.draftId))
        val old = reactiveMongoTemplate.findOne(query, TextSegmentEntity::class.java).awaitSingleOrNull()
        if (old == null) {
            logger.warn("字幕片段不存在，textSegmentId={}", req.textSegmentId)
            throw IllegalArgumentException("字幕片段不存在")
        }
        val updatedList = addToTextAnimationAndEffectList(old.textAnimationAndEffects, req)
        val update = Update()
            .set("textAnimationAndEffects", updatedList)
            .set("updateTime", LocalDateTime.now())
        reactiveMongoTemplate.updateFirst(query, update, TextSegmentEntity::class.java).awaitSingle()
        logger.info("字幕片段动画和特效添加成功，textSegmentId={}", req.textSegmentId)
        return req.textSegmentId
    }

    /**
     * 将新的动画和特效添加到列表中
     */
    private fun addToTextAnimationAndEffectList(
        oldList: List<TextAnimationAndEffect>,
        req: TextAnimationAndEffectReqDto
    ): List<TextAnimationAndEffect> {
        val newEffect = TextAnimationAndEffect(
            type = req.type,
            duration = req.duration,
            bubbleEffectId = req.bubbleEffectId,
            bubbleResourceId = req.bubbleResourceId,
            flowerEffectId = req.flowerEffectId
        )
        return oldList + newEffect
    }
} 