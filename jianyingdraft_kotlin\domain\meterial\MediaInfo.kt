package com.esther.jianyingdraft.domain.meterial

import io.swagger.v3.oas.annotations.media.Schema

/**
 * 媒体文件信息数据类
 * 包含媒体文件的完整信息：文件名、路径、大小、格式、类型、尺寸、时长等
 *
 * <AUTHOR>
 */
@Schema(description = "媒体文件信息")
data class MediaInfo(
    @Schema(description = "文件名")
    val fileName: String,
    @Schema(description = "文件路径")
    val absolutePath: String,
    @Schema(description = "文件大小")
    val fileSize: Long,
    @Schema(description = "文件MIME类型")
    val mimeType: String,

    @Schema(description = "文件格式")
    val type: String,

    @Schema(description = "宽度")
    val width: Int? = null,

    @Schema(description = "高度")
    val height: Int? = null,

    @Schema(description = " 时长(微秒)，静态图片为null")
    val durationMicroseconds: Long? = null,
    @Schema(description = "时长(秒)，静态图片为null")
    val durationSeconds: String? = null
)