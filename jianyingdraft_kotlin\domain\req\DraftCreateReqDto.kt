package com.esther.jianyingdraft.domain.req

import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
@Schema(description = "创建草稿请求参数")
data class DraftCreateReqDto(
    @Schema(description = "草稿宽度")
    val width: Int = 1920,
    @Schema(description = "草稿高度")
    val height: Int = 1080,
    @Schema(description = "帧率")
    val fps: Int = 30,
    @Schema(description = "草稿名称")
    var name: String? = null,
    @Schema(description = "草稿路径")
    var draftPath: String,

    @Schema(description = "apiKey")
    var apiKey:String? = null
)
