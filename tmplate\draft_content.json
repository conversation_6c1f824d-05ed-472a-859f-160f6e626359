{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 5100000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "91E08AC5-22FB-47e2-9AA0-7DC300FAEA2B", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [{"id": "36f88a9e98824adebb43073711f64ded", "fade_in_duration": 1000000, "fade_out_duration": 0, "fade_type": 0, "type": "audio_fade"}], "audio_track_indexes": [], "audios": [{"app_id": 0, "category_id": "", "category_name": "local", "check_flag": 3, "copyright_limit_type": "none", "duration": 5185000, "effect_id": "", "formula_id": "", "id": "ffdb7367953f379daddd00230650c504", "local_material_id": "ffdb7367953f379daddd00230650c504", "music_id": "ffdb7367953f379daddd00230650c504", "name": "audio.mp3", "path": "D:\\pythonProject\\MyProject\\pyJianYingDraft\\readme_assets\\tutorial\\audio.mp3", "source_platform": 0, "type": "extract_music", "wave_points": []}], "beats": [], "canvases": [{"id": "2e0226e207754bc1a4196fc525228cb7", "type": "canvas_blur", "blur": 0.0625, "color": "#00000000", "source_platform": 0}], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [{"apply_target_type": 0, "effect_id": "361595", "id": "429a43afdb0544bfa2b6d69e50c9a348", "resource_id": "6742029398926430728", "type": "text_shape", "value": 1.0}, {"apply_target_type": 0, "effect_id": "7296357486490144036", "id": "f83a46dec7f5476bb39693ccd7f9e1a6", "resource_id": "7296357486490144036", "type": "text_effect", "value": 1.0, "source_platform": 1}], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [{"id": "e3c69b6b4eac4066be7043a147387300", "type": "sticker_animation", "multi_language_current": "none", "animations": [{"anim_adjust_params": null, "platform": "all", "panel": "video", "material_type": "video", "name": "斜切", "id": "10696371", "type": "in", "resource_id": "7210657307938525751", "start": 0, "duration": 700000}]}, {"id": "9b0eaa96ca784cae872cd1dda507e003", "type": "sticker_animation", "multi_language_current": "none", "animations": [{"anim_adjust_params": null, "platform": "all", "panel": "", "material_type": "sticker", "name": "故障闪动", "id": "15261509", "type": "out", "resource_id": "7244102414377161276", "start": 3200000, "duration": 1000000}]}], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "speeds": [{"curve_speed": null, "id": "8cfeae54ed4546afb8703ef9a2acfca8", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "7fd5e28886824eb6b99952aeece0d67d", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "cfa3461ee1de4928bb25ae3a3ef2bb5d", "mode": 0, "speed": 1.0, "type": "speed"}], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [{"id": "f2a5ea0215644572a110e67bafa571b7", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 0.0]}}}, \"range\": [0, 23], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": [], \"font\": {\"id\": \"7290445778273702455\", \"path\": \"C:/文轩体.ttf\"}, \"effectStyle\": {\"id\": \"7296357486490144036\", \"path\": \"C:\"}}], \"text\": \"据说pyJianYingDraft效果还不错?\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}], "time_marks": [], "transitions": [{"category_id": "", "category_name": "", "duration": 500000, "effect_id": "25265947", "id": "3fb6f328be4d43c9b4a7f01310d8ac11", "is_overlap": true, "name": "信号故障", "platform": "all", "resource_id": "7288149307197231676", "type": "transition"}], "video_effects": [], "video_trackings": [], "videos": [{"audio_fade": null, "category_id": "", "category_name": "local", "check_flag": 63487, "crop": {"upper_left_x": 0.0, "upper_left_y": 0.0, "upper_right_x": 1.0, "upper_right_y": 0.0, "lower_left_x": 0.0, "lower_left_y": 1.0, "lower_right_x": 1.0, "lower_right_y": 1.0}, "crop_ratio": "free", "crop_scale": 1.0, "duration": 5000000, "height": 1080, "id": "e940887fd7b137cb9aac108c247075d7", "local_material_id": "", "material_id": "e940887fd7b137cb9aac108c247075d7", "material_name": "video.mp4", "media_path": "", "path": "D:\\pythonProject\\MyProject\\pyJianYingDraft\\readme_assets\\tutorial\\video.mp4", "type": "video", "width": 2046}, {"audio_fade": null, "category_id": "", "category_name": "local", "check_flag": 63487, "crop": {"upper_left_x": 0.0, "upper_left_y": 0.0, "upper_right_x": 1.0, "upper_right_y": 0.0, "lower_left_x": 0.0, "lower_left_y": 1.0, "lower_right_x": 1.0, "lower_right_y": 1.0}, "crop_ratio": "free", "crop_scale": 1.0, "duration": 900000, "height": 259, "id": "e1073a70d2cf3973b1102255f90c422b", "local_material_id": "", "material_id": "e1073a70d2cf3973b1102255f90c422b", "material_name": "sticker.gif", "media_path": "", "path": "D:\\pythonProject\\MyProject\\pyJianYingDraft\\readme_assets\\tutorial\\sticker.gif", "type": "video", "width": 312}], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "e249da56e65b4368a22196d0484373f4", "is_default_name": false, "name": "audio", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "eb838e155ccb412387f3183dbbc7e470", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 0, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 5000000}, "speed": 1.0, "volume": 0.6, "extra_material_refs": ["8cfeae54ed4546afb8703ef9a2acfca8", "36f88a9e98824adebb43073711f64ded"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}], "type": "audio"}, {"attribute": 0, "flag": 0, "id": "41754fa3f28548ee944ff964e4f2d128", "is_default_name": false, "name": "video", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "985c212b6d8b458f9660af770626e9c9", "material_id": "e940887fd7b137cb9aac108c247075d7", "target_timerange": {"start": 0, "duration": 4200000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 4200000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["7fd5e28886824eb6b99952aeece0d67d", "e3c69b6b4eac4066be7043a147387300", "3fb6f328be4d43c9b4a7f01310d8ac11"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "hdr_settings": {"intensity": 1.0, "mode": 1, "nits": 1000}, "render_index": 0}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "b9ed326c33c7450f8638c35d43a1c839", "material_id": "e1073a70d2cf3973b1102255f90c422b", "target_timerange": {"start": 4200000, "duration": 900000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 900000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["cfa3461ee1de4928bb25ae3a3ef2bb5d", "2e0226e207754bc1a4196fc525228cb7"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "hdr_settings": {"intensity": 1.0, "mode": 1, "nits": 1000}, "render_index": 0}], "type": "video"}, {"attribute": 0, "flag": 0, "id": "f8d9324a9e53463588e921674620346b", "is_default_name": false, "name": "text", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "9dff869ffc364c4dacfd367f1f89532b", "material_id": "f2a5ea0215644572a110e67bafa571b7", "target_timerange": {"start": 0, "duration": 4200000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["18e008ee9ab24c9e90c4ec8c7e76893d", "9b0eaa96ca784cae872cd1dda507e003", "429a43afdb0544bfa2b6d69e50c9a348", "f83a46dec7f5476bb39693ccd7f9e1a6"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": -0.8}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}], "type": "text"}], "update_time": 0, "version": 360000}