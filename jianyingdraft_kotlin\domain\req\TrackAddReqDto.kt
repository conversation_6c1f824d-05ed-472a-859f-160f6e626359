package com.esther.jianyingdraft.domain.req

import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 操作剪映轨道, 可以添加音频, 视频,和文本轨道
 */
@Schema(description = "轨道添加请求参数")
data class TrackAddReqDto(
    @Schema(description = "草稿id")
    val draftId: String,
    @Schema(description = "轨道类型")
    val trackType: String,

    /**
     * 轨道名称. 仅在创建第一个同类型轨道时允许不指定.
     */
    @Schema(description = "轨道名称. 仅在创建第一个同类型轨道时允许不指定.")
    val trackName: String? = null,
    /**
     *  轨道是否静音. 默认不静音.
     */
    @Schema(description = "轨道是否静音. 默认不静音.")
    val mute: Boolean = false,
    /**
     * 相对(同类型轨道的)图层位置, 越高越接近前景. 默认为0.
     */
    @Schema(description = "相对(同类型轨道的)图层位置, 越高越接近前景. 默认为0.")
    val relativeIndex: Int = 0,
    /**
     * 绝对图层位置, 越高越接近前景. 此参数将直接覆盖相应片段的`render_index`属性, 供有经验的用户使用.
     * 此参数不能与`relative_index`同时使用.
     */
    @Schema(description = "绝对图层位置, 越高越接近前景. 此参数将直接覆盖相应片段的render_index属性, 供有经验的用户使用. 此参数不能与relative_index同时使用.")
    val absoluteIndex: Int? = null,
) {
    companion object {
        const val TRACK_TYPE_VIDEO = "video"
        const val TRACK_TYPE_AUDIO = "audio"
        const val TRACK_TYPE_TEXT = "text"
    }
}