package com.esther.jianyingdraft.domain.rep

import com.esther.jianyingdraft.entity.AudioSegmentEntity
import com.esther.jianyingdraft.entity.TextSegmentEntity
import com.esther.jianyingdraft.entity.VideoSegmentEntity
import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/22
 * @des 草稿结果返回参数
 */
@Schema(description = "草稿结果返回参数")
data class DraftResultResDto(
    @Schema(description = "草稿id")
    val draftId: String,
    @Schema(description = "草稿名称")
    val draftName: String? = null,
    @Schema(description = "草稿内容")
    val draftContent: Map<String, Any?>? = null,
    @Schema(description = "音频轨道")
    var audioTracks: List<AudioTrack> = emptyList(),
    @Schema(description = "视频轨道")
    var videoTracks: List<VideoTrack> = emptyList(),
    @Schema(description = "字幕轨道")
    var textTracks: List<TextTrack> = emptyList(),
)

@Schema(description = "音频轨道")
data class AudioTrack(
    @Schema(description = "轨道id")
    val trackId: String,

    @Schema(description = "轨道名称")
    val trackName: String? = null,

    @Schema(description = "轨道下的音频")
    val audioSegments: List<AudioSegmentEntity> = emptyList(),
)

@Schema(description = "视频轨道")
data class VideoTrack(
    @Schema(description = "轨道id")
    val trackId: String,

    @Schema(description = "轨道名称")
    val trackName: String? = null,

    @Schema(description = "轨道下的视频")
    val videoSegments: List<VideoSegmentEntity> = emptyList(),
)

@Schema(description = "字幕轨道")
data class TextTrack(
    @Schema(description = "轨道id")
    val trackId: String,

    @Schema(description = "轨道名称")
    val trackName: String? = null,

    @Schema(description = "轨道下的字幕")
    val textSegments: List<TextSegmentEntity> = emptyList(),
)
