package com.esther.jianyingdraft.utils

import com.esther.jianyingdraft.domain.Timerange
import com.esther.jianyingdraft.domain.TimerangeDto
import com.esther.jianyingdraft.domain.meterial.MediaInfo
import com.esther.jianyingdraft.exception.SysException
import org.slf4j.LoggerFactory
import java.util.Locale.getDefault

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
object TimeUtils {

    private val logger = LoggerFactory.getLogger(TimeUtils::class.java)

    const val SEC = 1_000_000L // 1秒 = 1,000,000 微秒
    fun calculateTimeline(timerange: Timerange): TimerangeDto {
        if (timerange.duration == null) {
            throw SysException.systemError("duration不能为空")
        }
        if (timerange.start == null) {
            throw SysException.systemError("start不能为空")
        }
        val startTimeMicros = tim(timerange.start!!)
        val durationMicros = tim(timerange.duration!!)
        return TimerangeDto(startTimeMicros, durationMicros)
    }

    fun handDuration(sourceTimerange: Timerange, mediaInfo: MediaInfo): Timerange {
        if (sourceTimerange.duration == null && mediaInfo.durationSeconds != null) {
            sourceTimerange.duration = mediaInfo.durationSeconds
            return sourceTimerange
        } else if (sourceTimerange.duration != null) {
            return sourceTimerange
        } else {
            logger.warn("音频片段时长无效，请检查资源是否合法")
            throw SysException.invalidParam("音频片段时长无效")
        }
    }

    fun tim(inp: Any): Long {
        return when (inp) {
            is Int -> inp.toLong()
            is Float -> inp.toLong()
            is Double -> inp.toLong()
            is String -> {
                val result = try {
                    inp.toLong()
                } catch (_: Exception) {
                    null
                }
                if (result != null) {
                    return result
                }
                var sign = 1L
                var inputString = inp.trim().lowercase(getDefault())

                if (inputString.startsWith("-")) {
                    sign = -1L
                    inputString = inputString.substring(1)
                }

                var totalTime: Long = 0

                val timeUnits = listOf(
                    'h' to (3600 * SEC),
                    'm' to (60 * SEC),
                    's' to SEC
                )

                var lastIndex = 0
                for ((unitChar, factor) in timeUnits) {
                    val unitIndex = inputString.indexOf(unitChar)
                    if (unitIndex == -1) continue

                    val valueString = inputString.substring(lastIndex, unitIndex)
                    try {
                        totalTime += (valueString.toDouble() * factor).toLong()
                    } catch (e: NumberFormatException) {
                        throw IllegalArgumentException("Invalid number format in time string: $valueString", e)
                    }
                    lastIndex = unitIndex + 1
                }
                (totalTime * sign)
            }

            else -> throw IllegalArgumentException("Unsupported input type. Expected String, Int, Float, or Double.")
        }
    }
}