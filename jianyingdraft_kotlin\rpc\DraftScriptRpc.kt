package com.esther.jianyingdraft.rpc

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.domain.rep.DraftResultResDto
import com.esther.jianyingdraft.domain.req.DraftCreateReqDto
import org.springframework.stereotype.Component
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.service.annotation.HttpExchange
import org.springframework.web.service.annotation.PostExchange
import reactor.core.publisher.Mono

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
@Component
@HttpExchange("\${jianying.draft.draft-url}/api/v1/drafts")
interface DraftScriptRpc {
    @PostExchange("/export_script")
    fun exportScript(@RequestBody reqDto: DraftResultResDto): Mono<DataResponse<MutableMap<String, Any?>>>
}