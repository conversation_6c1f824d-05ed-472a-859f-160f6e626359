package com.esther.jianyingdraft.domain.text

import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
/**
 * 字体样式类
 */
@Schema(description = "字体样式类")
data class TextStyle(
    /**
     * 字体大小
     */
    @Schema(description = "字体大小")
    val size: Float = 8.0f,

    /**
     * 是否加粗
     */
    @Schema(description = "是否加粗")
    val bold: Boolean = false,
    /**
     * 是否斜体
     */
    @Schema(description = "是否斜体")
    val italic: Boolean = false,
    /**
     * 是否加下划线
     */
    @Schema(description = "是否加下划线")
    val underline: Boolean = false,

    /**
     * 字体颜色, RGB三元组, 取值范围为[0, 1]
     */
    @Schema(description = "字体颜色")
    val color: List<Float> = listOf(1.0f, 1.0f, 1.0f), // 默认白色
    /**
     * 字体不透明度
     */
    @Schema(description = "字体不透明度")
    val alpha: Float = 1.0f,

    /**
     * 对齐方式
     * 0: 左对齐, 1: 居中, 2: 右对齐
     */
    @Schema(description = "对齐方式")
    val align: Int = 0,
    /**
     * 是否为竖排文本
     */
    @Schema(description = "是否为竖排文本")
    val vertical: Boolean = false,

    /**
     * 字符间距
     */
    @Schema(description = "字符间距")
    val letterSpacing: Int = 0,
    /**
     * 行间距
     */
    @Schema(description = "行间距")
    val lineSpacing: Int = 0,

    /**
     * 是否自动换行
     */
    @Schema(description = "是否自动换行")
    val autoWrapping: Boolean = false,
    /**
     * 最大行宽, 取值范围为[0, 1]
     */
    @Schema(description = "最大行宽")
    val maxLineWidth: Float = 0.82f
) {
    /**
     * 初始化字体样式设置。
     *
     * @param size 字体大小, 默认为8.0
     * @param bold 是否加粗, 默认为否
     * @param italic 是否斜体, 默认为否
     * @param underline 是否加下划线, 默认为否
     * @param color 字体颜色, RGB三元组, 取值范围为[0, 1], 默认为白色
     * @param alpha 字体不透明度, 取值范围[0, 1], 默认不透明
     * @param align 对齐方式, 0: 左对齐, 1: 居中, 2: 右对齐, 默认为左对齐
     * @param vertical 是否为竖排文本, 默认为否
     * @param letterSpacing 字符间距, 定义与剪映中一致, 默认为0
     * @param lineSpacing 行间距, 定义与剪映中一致, 默认为0
     * @param autoWrapping 是否自动换行, 默认关闭
     * @param maxLineWidth 每行最大行宽占屏幕宽度比例, 取值范围为[0, 1], 默认为0.82
     */
    // 在 Kotlin 中，data class 的主构造函数已经包含了所有这些参数和默认值，
    // 所以通常不需要显式定义一个像 Python 中那样的 __init__ 方法。
    // 这里保留 KDoc 注释以说明对应的逻辑。
}

/**
 * 文本描边的参数
 */
@Schema(description = "文本描边参数")
data class TextBorder(
    /**
     * 描边不透明度
     */
    @Schema(description = "描边不透明度")
    val alpha: Float = 1.0f,
    /**
     * 描边颜色, RGB三元组, 取值范围为[0, 1]
     */
    @Schema(description = "描边颜色")
    val color: List<Float> = listOf(0.0f, 0.0f, 0.0f), // Kotlin 中使用 Triple 替代 Python 的 Tuple
    /**
     * 描边宽度
     */
    private val mappedWidth: Float = 40.0f
) {
    // 这是一个只读属性，用于计算转换后的宽度
    // 可以在这里进行映射，或者在需要使用的地方再进行计算
    val width: Float
        get() = mappedWidth / 100.0f * 0.2f

    /**
     * 初始化图像调节设置, 默认不作任何图像变换
     *
     * @param alpha 图像不透明度, 0-1. 默认为1.0.
     * @param color 描边颜色, RGB三元组, 取值范围为[0, 1]. 默认为(0.0, 0.0, 0.0) 黑色.
     * @param width 描边宽度, 与剪映中一致, 取值范围为[0, 100]. 默认为40.0.
     *
     * 注意：Python 代码中有一个宽度映射 `width / 100.0 * 0.2`。
     * 在 Kotlin 中，我将其作为一个只读属性 `mappedWidth` 来处理。
     * 你也可以选择在构造函数内部直接计算并存储，但这会使得传入的 `width` 参数不再是原始值。
     * 通常，建议保留原始输入值，如果需要转换后的值则通过计算属性提供。
     */
    // 在 Kotlin 中，data class 的主构造函数已经包含了所有这些参数和默认值，
    // 所以通常不需要显式定义一个像 Python 中那样的 __init__ 方法。
    // 这里保留 KDoc 注释以说明对应的逻辑。
}

/**
 * 字体背景设置
 */
@Schema(description = "字体背景设置")
data class TextBackground(
    /**
     * 背景样式
     */
    @Schema(description = "背景样式, 1和2分别对应剪映中的两种样式, 默认为1")
    val style: Int = 1, // Kotlin 中没有 Literal 类型，所以直接用 Int
    /**
     * 背景不透明度
     */
    @Schema(description = "背景不透明度, 与剪映中一致, 取值范围[0, 1], 默认为1.0")
    val alpha: Double = 1.0,
    /**
     * 背景颜色, 格式为'#RRGGBB'
     */
    @Schema(description = "背景颜色, 格式为'#RRGGBB'")
    val color: String = "#000000", // 颜色是必传参数，没有默认值
    /**
     * 背景圆角半径
     */
    @Schema(description = "背景圆角半径, 与剪映中一致, 取值范围[0, 1], 默认为0.0")
    val roundRadius: Double = 0.0,
    /**
     * 背景高度
     */
    @Schema(description = "背景高度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14")
    val height: Double = 0.14,
    /**
     * 背景宽度
     */
    @Schema(description = "背景宽度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14")
    val width: Double = 0.14,
    /**
     * 背景水平偏移
     */
    private val horizontal: Double = 0.5, // 注意这里是构造函数参数
    /**
     * 背景竖直偏移
     */
    private val vertical: Double = 0.5 // 注意这里是构造函数参数
) {
    // 将 horizontal_offset 和 vertical_offset 的计算逻辑放入 init 块或作为计算属性
    // 这里选择作为计算属性（getter），更符合Kotlin习惯，避免在数据类主构造函数中进行复杂逻辑
    @Schema(description = "背景水平偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5")
    val horizontalOffset: Double = horizontal * 2 - 1
    @Schema(description = "背景竖直偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5")
    val verticalOffset: Double = vertical * 2 - 1
}