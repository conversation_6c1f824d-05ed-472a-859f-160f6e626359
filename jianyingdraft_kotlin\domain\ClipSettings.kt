package com.esther.jianyingdraft.domain

import io.swagger.v3.oas.annotations.media.Schema

/**
 * 素材片段的图像调节设置
 */
@Schema(description = "素材片段的图像调节设置")
data class ClipSettings(
    /**
     * 图像不透明度, 0-1
     */
    @Schema(description = "图像不透明度, 0-1")
    val alpha: Float = 1.0f,
    /**
     * 是否水平翻转
     */
    @Schema(description = "是否水平翻转")
    val flipHorizontal: Boolean = false,
    /**
     * 是否垂直翻转
     */
    @Schema(description = "是否垂直翻转")
    val flipVertical: Boolean = false,
    /**
     * 顺时针旋转的**角度**, 可正可负
     */
    @Schema(description = "顺时针旋转的**角度**, 可正可负")
    val rotation: Float = 0.0f,
    /**
     * 水平缩放比例
     */
    @Schema(description = "水平缩放比例")
    val scaleX: Float = 1.0f,
    /**
     * 垂直缩放比例
     */
    @Schema(description = "垂直缩放比例")
    val scaleY: Float = 1.0f,
    /**
     * 水平位移, 单位为半个画布宽
     */
    @Schema(description = "水平位移, 单位为半个画布宽")
    val transformX: Float = 0.0f,
    /**
     * 垂直位移, 单位为半个画布高
     */
    @Schema(description = "垂直位移, 单位为半个画布高")
    val transformY: Float = 0.0f
) {
    /**
     * 初始化图像调节设置, 默认不作任何图像变换
     *
     * @param alpha 图像不透明度, 0-1. 默认为1.0.
     * @param flipHorizontal 是否水平翻转. 默认为False.
     * @param flipVertical 是否垂直翻转. 默认为False.
     * @param rotation 顺时针旋转的**角度**, 可正可负. 默认为0.0.
     * @param scaleX 水平缩放比例. 默认为1.0.
     * @param scaleY 垂直缩放比例. 默认为1.0.
     * @param transformX 水平位移, 单位为半个画布宽. 默认为0.0.
     * @param transformY 垂直位移, 单位为半个画布高. 默认为0.0. 参考: 剪映导入的字幕似乎取此值为-0.8
     */
}