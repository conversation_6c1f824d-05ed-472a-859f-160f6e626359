package com.esther.jianyingdraft.domain.audio

import com.esther.jianyingdraft.domain.req.Resource
import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @des des
 */
@Schema(description = "音频特效请求参数")
data class AudioEffectReqDto(
    @Schema(description = "素材所属的 draftId")
    val draftId: String,

    @Schema(description = "素材片段的id")
    val segmentId: String,

    @Schema(description = "音频素材片段特效")
    val audioEffects: List<AudioEffect>
)

data class AudioEffect(
    @Schema(description = "音效类型, 一类音效只能添加一个, 根据 resourceId 去重.")
    val effectType: Resource,
    @Schema(description = "音效参数")
    val params: List<Float>? = null
)