package com.esther.jianyingdraft.service.impl

import com.esther.jianyingdraft.domain.audio.AudioEffect
import com.esther.jianyingdraft.domain.audio.AudioEffectReqDto
import com.esther.jianyingdraft.domain.req.AudioFadeEffectReqDto
import com.esther.jianyingdraft.domain.req.AudioKeyframeReqDto
import com.esther.jianyingdraft.domain.req.MediaSegmentAddReqDto
import com.esther.jianyingdraft.entity.AudioKeyframe
import com.esther.jianyingdraft.entity.AudioSegmentEntity
import com.esther.jianyingdraft.exception.SysException
import com.esther.jianyingdraft.service.AudioSegmentService
import com.esther.jianyingdraft.utils.DraftUtils
import com.esther.jianyingdraft.utils.MediaInfoExtractor
import com.esther.jianyingdraft.utils.TimeUtils
import kotlinx.coroutines.reactive.awaitSingle
import kotlinx.coroutines.reactor.awaitSingleOrNull
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 音频片段服务实现类，实现音频片段相关操作。
 * <AUTHOR>
 */
@Service
class AudioSegmentServiceImpl @Autowired constructor(
    private val reactiveMongoTemplate: ReactiveMongoTemplate,
) : AudioSegmentService {
    private val logger = LoggerFactory.getLogger(AudioSegmentServiceImpl::class.java)

    /**
     * 添加音频片段到MongoDB
     * @param req 音频片段添加请求参数
     * @return 新增音频片段的id
     */
    override suspend fun addAudioSegment(req: MediaSegmentAddReqDto): String {
        logger.info("开始添加音频片段，draftId={}, resourcePath={}, afterSegmentId={}", 
            req.draftId, req.resourcePath, req.afterSegmentId)
        
        if (req.resourcePath.isBlank()) {
            logger.warn("音频片段资源路径不能为空")
            throw SysException.invalidParam("音频片段资源路径不能为空")
        }
        
        // 提取媒体信息
        val extractMediaInfo = MediaInfoExtractor.extractMediaInfo(req.resourcePath)
        if (!extractMediaInfo.success || extractMediaInfo.data == null) {
            logger.warn("音频片段资源路径无效，请检查资源是否合法")
            throw SysException.invalidParam("音频片段资源无效")
        }
        
        // 权限检查
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        
        // 使用TimeUtils.handDuration计算实际的duration
        val durationCalculatedTimerange = TimeUtils.handDuration(req.targetTimerange, extractMediaInfo.data)
        
        // 构建完整的目标时间范围（支持afterSegmentId自动计算start时间）
        val completeTargetTimerange = DraftUtils.buildCompleteTargetTimerange(
            originalTimerange = req.targetTimerange,
            afterSegmentId = req.afterSegmentId,
            calculatedDuration = durationCalculatedTimerange.duration!!,
            mongoTemplate = reactiveMongoTemplate
        )
        
        logger.debug("完整的目标时间范围: start={}, duration={}", 
            completeTargetTimerange.start, completeTargetTimerange.duration)
        
        // 检查同一轨道上是否存在重叠片段
        DraftUtils.checkSegmentOverlapInTrack(
            draftId = req.draftId,
            trackId = req.trackId,
            newSegmentTimerange = completeTargetTimerange,
            segmentType = "audio",
            excludeSegmentId = null, // 新增操作，不排除任何片段
            mongoTemplate = reactiveMongoTemplate
        )
        
        val id = ObjectId().toHexString()
        val now = LocalDateTime.now()
        val entity = AudioSegmentEntity(
            id = id,
            draftId = req.draftId,
            targetTimerange = completeTargetTimerange,
            realTargetTimerange = completeTargetTimerange.let { TimeUtils.calculateTimeline(it) },
            realSourceTimerange = req.sourceTimerange?.let { TimeUtils.calculateTimeline(it) },
            sourceTimerange = req.sourceTimerange,
            speed = req.speed,
            trackId = req.trackId,
            volume = req.volume,
            mediaInfo = extractMediaInfo.data,
            resourcePath = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\${req.draftId}\\${extractMediaInfo.data.fileName}",
            createTime = now,
            updateTime = now
        )
        reactiveMongoTemplate.save(entity).awaitSingle()
        logger.info("音频片段添加成功，id={}, 最终时间范围: start={}, duration={}", 
            id, completeTargetTimerange.start, completeTargetTimerange.duration)
        return id
    }

    /**
     * 给音频片段添加或更新淡入淡出特效
     * @param req 音频淡入淡出特效请求参数
     * @return 音频片段id
     */
    override suspend fun addAudioFadeEffect(req: AudioFadeEffectReqDto): String {
        logger.info(
            "开始为音频片段添加/更新淡入淡出特效，draftId={}, audioSegmentId={}",
            req.draftId,
            req.audioSegmentId
        )
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        // 查找音频片段是否存在
        val query = Query(Criteria.where("id").`is`(req.audioSegmentId))
        validata(query = query, req.audioSegmentId)
        // 更新fadeEffect字段
        val update = Update().set("fadeEffect", req.audioFade)
        reactiveMongoTemplate.updateFirst(query, update, AudioSegmentEntity::class.java).awaitSingle()
        logger.info("音频片段淡入淡出特效添加/更新成功，audioSegmentId={}", req.audioSegmentId)
        return req.audioSegmentId
    }

    /**
     * 验证音频片段是否存在
     * @param query 查询条件
     * @param audioSegmentId 音频片段ID
     * @throws SysException 当音频片段不存在时抛出异常
     */
    private suspend fun validata(
        query: Query,
        audioSegmentId: String
    ) {
        val segment = reactiveMongoTemplate.findOne(query, AudioSegmentEntity::class.java).awaitSingleOrNull()
        if (segment == null) {
            logger.warn("音频片段不存在，audioSegmentId={}", audioSegmentId)
            throw SysException.notFound("音频片段不存在")
        }
    }

    /**
     * 给音频片段批量添加关键帧
     * @param req 关键帧请求参数，支持一次添加多个关键帧
     * @return 音频片段id
     * @throws SysException 当音频片段不存在或请求参数无效时抛出异常
     */
    override suspend fun addAudioKeyframe(req: AudioKeyframeReqDto): String {
        if (req.keyframes.isEmpty()) {
            logger.warn("关键帧列表为空，audioSegmentId={}", req.audioSegmentId)
            throw SysException.invalidParam("关键帧列表不能为空")
        }
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        logger.info(
            "开始为音频片段批量添加关键帧，draftId={}, audioSegmentId={}, 关键帧数量={}",
            req.draftId,
            req.audioSegmentId,
            req.keyframes.size
        )

        req.keyframes.forEach { keyframe ->
            logger.info(
                "关键帧详情：timeOffset={}, volume={}",
                keyframe.timeOffset,
                keyframe.volume
            )
        }

        // 验证输入的关键帧列表中是否有重复的timeOffset和volume组合
        val keyframeCombinations = req.keyframes.map { "${it.timeOffset}-${it.volume}" }
        val duplicates = keyframeCombinations.groupingBy { it }.eachCount().filter { it.value > 1 }
        if (duplicates.isNotEmpty()) {
            logger.warn("发现重复的关键帧组合：{}", duplicates.keys)
            throw SysException.invalidParam("关键帧列表中存在重复的timeOffset和volume组合")
        }

        // 查找音频片段是否存在
        val query = Query(Criteria.where("id").`is`(req.audioSegmentId))
        validata(query, req.audioSegmentId)

        // 获取当前音频片段的现有关键帧
        val currentSegment = reactiveMongoTemplate.findOne(query, AudioSegmentEntity::class.java).awaitSingle()
        val existingKeyframes = currentSegment.audioKeyframes ?: emptyList()

        logger.info("当前音频片段已有关键帧数量={}", existingKeyframes.size)

        // 转换请求为AudioKeyframe对象
        val newKeyframes = req.keyframes.map { keyframeData ->
            AudioKeyframe(
                timeOffset = keyframeData.timeOffset,
                volume = keyframeData.volume
            )
        }

        // 合并现有关键帧和新关键帧，根据timeOffset和volume组合去重（新的覆盖旧的）
        val mergedKeyframes = mergeAudioKeyframes(existingKeyframes, newKeyframes)

        logger.info("关键帧去重合并后，最终关键帧数量={}", mergedKeyframes.size)
        mergedKeyframes.forEach { keyframe ->
            logger.info("关键帧详情：timeOffset={}, volume={}", keyframe.timeOffset, keyframe.volume)
        }

        // 更新音频片段的关键帧列表
        val update = Update().set("audioKeyframes", mergedKeyframes)
        reactiveMongoTemplate.updateFirst(query, update, AudioSegmentEntity::class.java).awaitSingle()

        logger.info("音频片段关键帧批量添加成功，audioSegmentId={}", req.audioSegmentId)
        return req.audioSegmentId
    }

    /**
     * 合并音频关键帧列表，根据timeOffset和volume组合去重，新关键帧覆盖同组合的旧关键帧
     * @param existingKeyframes 现有关键帧列表
     * @param newKeyframes 新关键帧列表
     * @return 合并后的关键帧列表
     */
    private fun mergeAudioKeyframes(
        existingKeyframes: List<AudioKeyframe>,
        newKeyframes: List<AudioKeyframe>
    ): List<AudioKeyframe> {
        logger.debug("开始合并音频关键帧，现有关键帧数量={}, 新关键帧数量={}", existingKeyframes.size, newKeyframes.size)

        // 使用Map来根据timeOffset和volume组合去重，新的覆盖旧的
        val keyframeMap = mutableMapOf<String, AudioKeyframe>()

        // 先添加现有关键帧
        existingKeyframes.forEach { keyframe ->
            val key = "${keyframe.timeOffset}-${keyframe.volume}"
            keyframeMap[key] = keyframe
            logger.debug("添加现有关键帧：timeOffset={}, volume={}", keyframe.timeOffset, keyframe.volume)
        }

        // 再添加新关键帧，相同timeOffset和volume组合的会覆盖现有的
        newKeyframes.forEach { keyframe ->
            val key = "${keyframe.timeOffset}-${keyframe.volume}"
            val previousKeyframe = keyframeMap[key]
            keyframeMap[key] = keyframe
            if (previousKeyframe != null) {
                logger.info(
                    "关键帧覆盖：timeOffset={}, volume={} 的关键帧被新关键帧覆盖",
                    keyframe.timeOffset,
                    keyframe.volume
                )
            } else {
                logger.info("关键帧新增：timeOffset={}, volume={}", keyframe.timeOffset, keyframe.volume)
            }
        }

        val result = keyframeMap.values.toList()
        logger.debug("关键帧合并完成，最终关键帧数量={}", result.size)
        return result
    }

    /**
     * 给音频片段添加特效
     * @param req 特效请求参数列表
     * @return 音频片段id
     * @throws SysException 当音频片段不存在或请求参数无效时抛出异常
     */
    override suspend fun addAudioEffect(req: AudioEffectReqDto): String {
        if (req.audioEffects.isEmpty()) {
            logger.warn("音频特效请求列表为空")
            throw SysException.invalidParam("音频特效请求列表不能为空")
        }
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        logger.info(
            "开始为音频片段添加特效，draftId={}, segmentId={}, 特效数量={}", req.draftId, req.segmentId,
            req.audioEffects.size
        )

        // 查找音频片段是否存在
        val query = Query(Criteria.where("id").`is`(req.segmentId))
        validata(query, req.segmentId)

        // 获取当前音频片段的现有特效
        val currentSegment = reactiveMongoTemplate.findOne(query, AudioSegmentEntity::class.java).awaitSingle()
        val existingEffects = currentSegment.audioEffects ?: emptyList()

        logger.info("当前音频片段已有特效数量={}", existingEffects.size)

        // 转换请求为AudioEffect对象
        val newEffects = req.audioEffects.map { effectReq ->
            AudioEffect(
                effectType = effectReq.effectType,
                params = effectReq.params
            )
        }

        // 合并现有特效和新特效，根据resourceId去重（新的覆盖旧的）
        val mergedEffects = mergeAudioEffects(existingEffects, newEffects)

        logger.info("特效去重合并后，最终特效数量={}", mergedEffects.size)
        mergedEffects.forEach { effect ->
            logger.info(
                "特效详情：resourceId={}, resourceName={}, params={}",
                effect.effectType.resourceId,
                effect.effectType.resourceName,
                effect.params
            )
        }

        // 更新音频片段的特效列表
        val update = Update().set("audioEffects", mergedEffects)
        reactiveMongoTemplate.updateFirst(query, update, AudioSegmentEntity::class.java).awaitSingle()

        logger.info("音频片段特效添加成功，segmentId={}", req.segmentId)
        return req.segmentId
    }

    /**
     * 合并音频特效列表，根据resourceId去重，新特效覆盖同类型的旧特效
     * @param existingEffects 现有特效列表
     * @param newEffects 新特效列表
     * @return 合并后的特效列表
     */
    private fun mergeAudioEffects(
        existingEffects: List<AudioEffect>,
        newEffects: List<AudioEffect>
    ): List<AudioEffect> {
        logger.debug("开始合并音频特效，现有特效数量={}, 新特效数量={}", existingEffects.size, newEffects.size)

        // 使用Map来根据resourceId去重，新的覆盖旧的
        val effectMap = mutableMapOf<String, AudioEffect>()

        // 先添加现有特效
        existingEffects.forEach { effect ->
            effectMap[effect.effectType.resourceId] = effect
            logger.debug("添加现有特效：resourceId={}", effect.effectType.resourceId)
        }

        // 再添加新特效，相同resourceId的会覆盖现有的
        newEffects.forEach { effect ->
            val previousEffect = effectMap[effect.effectType.resourceId]
            effectMap[effect.effectType.resourceId] = effect
            if (previousEffect != null) {
                logger.info("特效覆盖：resourceId={} 的特效被新特效覆盖", effect.effectType.resourceId)
            } else {
                logger.info("特效新增：resourceId={}", effect.effectType.resourceId)
            }
        }

        val result = effectMap.values.toList()
        logger.debug("特效合并完成，最终特效数量={}", result.size)
        return result
    }
} 